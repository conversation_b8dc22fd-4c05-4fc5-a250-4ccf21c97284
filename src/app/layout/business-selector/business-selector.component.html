<div
  class="business-selector"
  [class.collapsed]="collapsed"
  tabindex="0"
  role="button"
  [attr.aria-label]="ariaLabel"
  (click)="openSelectorDialog()"
  (keydown.enter)="openSelectorDialog()"
  (keydown.space)="openSelectorDialog()"
>
  <ng-container *ngIf="!collapsed; else collapsedView">
    <div class="business-header">
      <div class="logo-group">
        <ng-container *ngFor="let b of selectedBusinesses.slice(0, 1)">
          <div class="business-logo-container">
            <img *ngIf="b.logoUrl" [src]="b.logoUrl" [alt]="b.name" class="business-logo" />
            <div *ngIf="!b.logoUrl" class="business-icon-container">
              <mat-icon class="business-icon">business</mat-icon>
            </div>
          </div>
        </ng-container>
      </div>
      <div class="business-info">
        <span class="business-name">
          {{ isOneView ? 'OneView: Multiple' : (selectedBusinesses[0]?.name || 'Select business') }}
        </span>
        <mat-chip *ngIf="isOneView" color="accent" class="oneview-chip" size="small">
          <mat-icon>visibility</mat-icon>
          OneView
        </mat-chip>
        <div class="business-subtitle" *ngIf="!isOneView">
          {{ selectedBusinesses.length }} location{{ selectedBusinesses.length !== 1 ? 's' : '' }}
        </div>
      </div>
    </div>
  </ng-container>
  <ng-template #collapsedView>
    <div class="business-selector-collapsed">
      <button mat-icon-button
              [matTooltip]="selectedBusinesses[0]?.name || 'Select business'"
              matTooltipPosition="right"
              aria-label="Select business"
              class="collapsed-business-btn">
        <mat-icon>business</mat-icon>
      </button>
    </div>
  </ng-template>
</div>
