@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');

.user-menu-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  padding: 8px;
  background: inherit; // Inherit sidebar background
  border-top: 1px solid rgba(0, 0, 0, 0.08);

  &.collapsed {
    padding: 8px 4px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
  }
}

.user-menu-trigger {
  width: 100%;
  padding: 12px 8px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(25, 118, 210, 0.1);
  transition: all 0.2s ease;
  min-height: 56px;
  display: flex;
  align-items: center;
  justify-content: flex-start;

  &:hover {
    background: rgba(25, 118, 210, 0.05);
    border-color: rgba(25, 118, 210, 0.2);
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
  }

  &:focus {
    outline: 2px solid var(--sm-primary-main, #1976d2);
    outline-offset: 2px;
  }

  .collapsed & {
    width: 48px !important;
    height: 48px !important;
    min-height: 48px !important;
    padding: 8px !important;
    border-radius: 50% !important;
    justify-content: center !important;
    margin: 0 !important;
    min-width: 48px !important;
  }
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  color: white;
  margin-right: 12px;
  flex-shrink: 0;

  mat-icon {
    font-size: 24px;
    width: 24px;
    height: 24px;
  }

  .collapsed & {
    margin-right: 0 !important;
    width: 32px !important;
    height: 32px !important;

    mat-icon {
      font-size: 20px !important;
      width: 20px !important;
      height: 20px !important;
    }
  }
}

.user-info {
  display: flex;
  align-items: center;
  width: 100%;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0; // Allow text truncation
}

.user-name {
  font-weight: 600;
  font-size: 0.95rem;
  color: var(--sm-text-primary, #1a1a1a);
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
  font-family: 'Inter', 'Roboto', sans-serif;
}

.user-role {
  font-size: 0.8rem;
  color: var(--sm-text-secondary, #666);
  font-weight: 400;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.dropdown-icon {
  margin-left: auto;
  color: var(--sm-text-secondary, #666);
  font-size: 18px;
  width: 18px;
  height: 18px;
  transition: transform 0.2s ease;

  .user-menu-trigger:hover & {
    color: var(--sm-primary-main, #1976d2);
  }
}

// Menu styles
.user-menu {
  .mat-mdc-menu-panel {
    min-width: 240px;
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(0, 0, 0, 0.08);
  }
}

.user-menu-header {
  padding: 16px;
  display: flex;
  align-items: center;
  background: rgba(25, 118, 210, 0.02);
}

.user-avatar-large {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;

  mat-icon {
    font-size: 28px;
    width: 28px;
    height: 28px;
  }
}

.user-info-menu {
  flex: 1;
  min-width: 0;

  .user-name {
    font-weight: 600;
    font-size: 1rem;
    color: var(--sm-text-primary, #1a1a1a);
    margin-bottom: 2px;
    max-width: none;
  }

  .user-email {
    font-size: 0.85rem;
    color: var(--sm-text-secondary, #666);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// Menu items
::ng-deep .user-menu .mat-mdc-menu-item {
  padding: 12px 16px;
  min-height: 48px;

  .mat-icon {
    margin-right: 12px;
    color: var(--sm-text-secondary, #666);
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

  &:hover .mat-icon {
    color: var(--sm-primary-main, #1976d2);
  }

  &.sign-out-item {
    color: var(--sm-error-main, #d32f2f);

    .mat-icon {
      color: var(--sm-error-main, #d32f2f);
    }

    &:hover {
      background: rgba(211, 47, 47, 0.04);
    }
  }
}

// Dark theme support
.dark-theme {
  .user-menu-trigger {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.2);
    }
  }

  .user-name {
    color: rgba(255, 255, 255, 0.9);
  }

  .user-role {
    color: rgba(255, 255, 255, 0.6);
  }

  .user-menu-header {
    background: rgba(255, 255, 255, 0.02);
  }
}

// Responsive adjustments
@media (max-width: 900px) {
  .user-menu-container {
    padding: 12px 16px;

    &.collapsed {
      padding: 12px 16px;
      justify-content: flex-start;
    }
  }

  .user-menu-trigger {
    .collapsed & {
      width: 100%;
      border-radius: 8px;
      justify-content: flex-start;
    }
  }

  .user-avatar {
    .collapsed & {
      margin-right: 12px;
      width: 40px;
      height: 40px;

      mat-icon {
        font-size: 24px;
        width: 24px;
        height: 24px;
      }
    }
  }
}
