import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-user-menu',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    MatDividerModule,
    MatTooltipModule
  ],
  template: `
    <div class="user-menu-container" [class.collapsed]="collapsed">
      <button
        mat-button
        [matMenuTriggerFor]="userMenu"
        class="user-menu-trigger"
        [matTooltip]="collapsed ? 'User Menu - ' + userName : ''"
        matTooltipPosition="right"
        aria-label="User menu">

        <!-- Collapsed state: Only avatar icon -->
        <div class="user-avatar" *ngIf="collapsed">
          <mat-icon>account_circle</mat-icon>
        </div>

        <!-- Expanded state: Avatar + name + dropdown arrow -->
        <div class="user-info" *ngIf="!collapsed">
          <div class="user-avatar">
            <mat-icon>account_circle</mat-icon>
          </div>
          <div class="user-details">
            <span class="user-name">{{ userName }}</span>
            <span class="user-role">{{ userRole }}</span>
          </div>
          <mat-icon class="dropdown-icon">expand_more</mat-icon>
        </div>
      </button>

      <mat-menu #userMenu="matMenu" class="user-menu" yPosition="above">
        <div class="user-menu-header" *ngIf="!collapsed">
          <div class="user-avatar-large">
            <mat-icon>account_circle</mat-icon>
          </div>
          <div class="user-info-menu">
            <div class="user-name">{{ userName }}</div>
            <div class="user-email">{{ userEmail }}</div>
          </div>
        </div>
        <mat-divider *ngIf="!collapsed"></mat-divider>

        <button mat-menu-item (click)="viewProfile()">
          <mat-icon>person</mat-icon>
          <span>View Profile</span>
        </button>

        <button mat-menu-item (click)="changeUser()">
          <mat-icon>swap_horiz</mat-icon>
          <span>Change User</span>
        </button>

        <mat-divider></mat-divider>

        <button mat-menu-item (click)="signOut()" class="sign-out-item">
          <mat-icon>logout</mat-icon>
          <span>Sign Out</span>
        </button>
      </mat-menu>
    </div>
  `,
  styleUrls: ['./user-menu.component.scss']
})
export class UserMenuComponent {
  @Input() collapsed = false;
  @Input() userName = 'John Doe';
  @Input() userRole = 'Manager';
  @Input() userEmail = '<EMAIL>';

  viewProfile() {
    console.log('View Profile clicked');
    // TODO: Navigate to profile page or open profile dialog
  }

  changeUser() {
    console.log('Change User clicked');
    // TODO: Implement user switching functionality
  }

  signOut() {
    console.log('Sign Out clicked');
    // TODO: Implement sign out functionality
  }
}
