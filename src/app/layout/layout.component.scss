// CSS Custom Properties for dynamic layout
:root {
  --sidebar-width-expanded: 240px;
  --sidebar-width-collapsed: 64px;
  --header-height: 64px;
  --transition-duration: 0.3s;
  --transition-timing: cubic-bezier(0.4, 0.0, 0.2, 1);
}

// Main App Shell - CSS Grid Layout with Dynamic Shifting
.app-shell {
  height: 100vh;
  width: 100vw;
  display: grid;
  grid-template-columns: var(--sidebar-width-expanded) 1fr;
  grid-template-rows: var(--header-height) 1fr;
  grid-template-areas:
    "sidebar header"
    "sidebar content";
  background: var(--sm-background-default, #fafafa);
  overflow: hidden;
  transition: grid-template-columns var(--transition-duration) var(--transition-timing);

  // Collapsed state - CRITICAL: Exact 64px width enforcement
  &.sidebar-collapsed {
    grid-template-columns: var(--sidebar-width-collapsed) 1fr;
  }

  // Mobile layout
  &.mobile-layout {
    grid-template-columns: 1fr;
    grid-template-rows: var(--header-height) 1fr;
    grid-template-areas:
      "header"
      "content";
  }
}

// Sidebar - CRITICAL: Exact width control
.app-sidebar {
  grid-area: sidebar;
  width: var(--sidebar-width-expanded);
  height: 100vh;
  background: var(--sm-background-paper, #ffffff);
  border-right: 1px solid var(--sm-border-color, #e0e0e0);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
  overflow: hidden;
  transition: width var(--transition-duration) var(--transition-timing);
  box-sizing: border-box;

  // Collapsed state - CRITICAL: Force exact 64px width
  &.collapsed {
    width: var(--sidebar-width-collapsed) !important;
    max-width: var(--sidebar-width-collapsed) !important;
    min-width: var(--sidebar-width-collapsed) !important;
    flex-basis: var(--sidebar-width-collapsed) !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
  }

  // Mobile states
  .mobile-layout & {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    transform: translateX(-100%);
    transition: transform var(--transition-duration) var(--transition-timing);
    z-index: 1100;

    &.mobile-open {
      transform: translateX(0);
    }
  }
}



// Sidebar Toggle Button
.sidebar-toggle-btn {
  position: absolute;
  top: 12px;
  right: -20px;
  z-index: 1001;
  background: var(--sm-background-paper, #ffffff);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all var(--transition-duration) var(--transition-timing);

  // Collapsed state positioning
  .app-sidebar.collapsed & {
    right: -20px; // Keep same position relative to collapsed sidebar
  }

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transform: scale(1.05);
  }

  button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--sm-primary-main, #1976d2);
    color: white;
    transition: all var(--transition-duration) var(--transition-timing);

    &:hover {
      background: var(--sm-primary-dark, #1565c0);
      transform: rotate(180deg);
    }

    mat-icon {
      transition: transform var(--transition-duration) var(--transition-timing);
    }
  }
}

// Main Content Area
.app-main {
  grid-area: main;
  display: grid;
  grid-template-rows: var(--header-height) 1fr;
  grid-template-areas:
    "header"
    "content";
  height: 100vh;
  overflow: hidden;
}

// Header - Dynamic positioning with sidebar
.app-header {
  grid-area: header;
  height: var(--header-height);
  background: var(--sm-background-paper, #ffffff);
  border-bottom: 1px solid var(--sm-border-color, #e0e0e0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 999;
  position: relative;
  transition: all var(--transition-duration) var(--transition-timing);
}

// Content - Dynamic width adjustment
.app-content {
  grid-area: content;
  background: var(--sm-background-default, #fafafa);
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  padding: 0;
  transition: all var(--transition-duration) var(--transition-timing);

  // Smooth scrolling
  scroll-behavior: smooth;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--sm-background-default, #fafafa);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--sm-text-secondary, #666);
    border-radius: 4px;

    &:hover {
      background: var(--sm-text-primary, #333);
    }
  }
}

// Mobile Backdrop
.mobile-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1050;
  backdrop-filter: blur(2px);
}

// Responsive Design
@media (max-width: 900px) {
  .sidebar-toggle-btn {
    display: none;
  }
}

@media (max-width: 600px) {
  .app-sidebar {
    .mobile-layout & {
      width: 100vw;
    }
  }
}

// Focus and Accessibility
.sidebar-toggle-btn button:focus {
  outline: 2px solid var(--sm-primary-main, #1976d2);
  outline-offset: 2px;
}

// Dark Theme Support
.dark-theme {
  .app-shell {
    background: var(--sm-background-default-dark, #121212);
  }

  .app-sidebar {
    background: var(--sm-background-paper-dark, #1e1e1e);
    border-right-color: rgba(255, 255, 255, 0.1);
  }

  .app-header {
    background: var(--sm-background-paper-dark, #1e1e1e);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .app-content {
    background: var(--sm-background-default-dark, #121212);
  }

  .sidebar-toggle-btn {
    background: var(--sm-background-paper-dark, #1e1e1e);
  }
}

// Loading State Support
.app-shell.loading {
  .app-content {
    opacity: 0.7;
    pointer-events: none;
  }
}

// Animation Performance Optimizations
.app-shell,
.app-sidebar,
.app-main,
.app-header,
.app-content {
  will-change: transform, width, grid-template-columns;
}

// Prevent layout shifts during transitions
.app-shell * {
  box-sizing: border-box;
}