.app-header {
  background: linear-gradient(90deg, #1976d2 0%, #42a5f5 100%);
  color: #fff;
  box-shadow: 0 2px 12px rgba(0,0,0,0.12);
  height: 64px;
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 999;
}

.app-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-weight: 700;
  font-size: 1.5rem;
  letter-spacing: -0.5px;
  text-shadow: 0 1px 3px rgba(0,0,0,0.3);
  font-family: 'Inter', 'Roboto', 'Open Sans', Arial, sans-serif;
}

.spacer {
  flex: 1;
}

.date-time-pill {
  display: flex;
  align-items: center;
  margin-left: 16px;
  padding: 6px 16px;
  border-radius: 20px;
  background: rgba(255,255,255,0.15);
  font-size: 0.95rem;
  font-weight: 500;
  letter-spacing: 0.02em;
  opacity: 0.95;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  gap: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.1);
}

.icon-btn {
  margin: 0 6px;
  transition: transform 0.2s ease, background 0.2s ease;
  border-radius: 8px;
  width: 40px;
  height: 40px;

  &:hover, &:focus {
    transform: translateY(-1px);
    background: rgba(255,255,255,0.15);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  }

  &:active {
    transform: translateY(0);
    background: rgba(255,255,255,0.2);
  }

  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }
}



.mat-badge-content, .mat-badge-content.mat-badge-warn {
  font-weight: bold;
  font-size: 0.75rem;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  background: var(--mui-palette-error-main, #d32f2f);
  color: #fff;
  box-shadow: 0 1px 2px rgba(0,0,0,0.10);
}

.mat-menu-item .mat-icon {
  margin-right: 8px;
}

.date-time {
  margin-left: 16px;
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 0.02em;
  opacity: 0.85;
}