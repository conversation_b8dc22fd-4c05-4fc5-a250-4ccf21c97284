@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');

// Hamburger Toggle Button Styles
.sidebar-toggle-container {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 1001;
}

.hamburger-toggle {
  width: 32px !important;
  height: 32px !important;
  min-width: 32px !important;
  padding: 0 !important;
  background: transparent !important;
  border-radius: 6px !important;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1) !important;

  &:hover {
    background: var(--sm-action-hover, rgba(0, 0, 0, 0.04)) !important;
  }

  &:focus {
    outline: 2px solid var(--sm-primary-main, #1976d2);
    outline-offset: 2px;
  }

  // When sidebar is collapsed, center the button
  &.collapsed {
    position: fixed;
    top: 12px;
    left: 16px; // Center in 64px sidebar
    right: auto;
  }
}

.hamburger-icon {
  width: 18px;
  height: 14px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  cursor: pointer;
}

.hamburger-line {
  width: 100%;
  height: 2px;
  background: var(--sm-text-secondary, #666);
  border-radius: 1px;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  transform-origin: center;

  .hamburger-toggle:hover & {
    background: var(--sm-text-primary, #333);
  }
}

// Hamburger icon remains static - no animation transformations
.hamburger-icon {
  .hamburger-line {
    // Keep all lines as simple horizontal bars
    transform: none;
    width: 100%;
    opacity: 1;
  }
}

// Dark theme support for hamburger menu and entire sidebar
.dark-theme {
  .hamburger-line {
    background: rgba(255, 255, 255, 0.7);

    .hamburger-toggle:hover & {
      background: rgba(255, 255, 255, 0.9);
    }
  }

  .hamburger-toggle {
    &:hover {
      background: rgba(255, 255, 255, 0.08) !important;
    }

    &:focus {
      outline-color: var(--sm-primary-light, #42a5f5);
    }
  }

  // Dark theme sidebar background and elements
  .sidebar-nav {
    background: linear-gradient(180deg, #1e1e1e 0%, #121212 100%) !important;

    &::before {
      background: linear-gradient(180deg, #42a5f5 0%, #1976d2 100%) !important;
    }
  }

  .sidebar-business-section {
    background: rgba(30, 30, 30, 0.95) !important;
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
  }

  .sidebar-section-label {
    color: #42a5f5 !important;
    text-shadow: 0 1px 2px rgba(66, 165, 245, 0.2) !important;
  }

  .mat-list-item {
    color: rgba(255, 255, 255, 0.87) !important;

    &:hover, &:focus {
      background: rgba(66, 165, 245, 0.1) !important;
      color: #42a5f5 !important;
    }

    &.active {
      background: #42a5f5 !important;
      color: #000 !important;

      .mat-icon {
        color: #000 !important;
      }
    }
  }

  .mat-icon {
    color: #42a5f5 !important;
  }

  .sidebar-divider {
    border-color: rgba(255, 255, 255, 0.1) !important;
  }

  // Dark theme scrollbar
  .mat-nav-list {
    &::-webkit-scrollbar-thumb {
      background: rgba(66, 165, 245, 0.3) !important;

      &:hover {
        background: rgba(66, 165, 245, 0.5) !important;
      }
    }
  }
}

// Mobile responsive behavior
@media (max-width: 900px) {
  .hamburger-toggle {
    &.collapsed {
      position: absolute; // Reset to relative positioning on mobile
      top: 12px;
      left: auto;
      right: 12px;
    }
  }
}

// CRITICAL: Host-level collapsed state with maximum specificity
:host(.collapsed) nav.sidebar-nav,
:host nav.sidebar-nav.collapsed {
  width: 64px !important;
  max-width: 64px !important;
  min-width: 64px !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
  flex-basis: 64px !important;
  flex-grow: 0 !important;
  flex-shrink: 0 !important;
}

.mat-nav-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px 0 100px 0; // Bottom padding for UserMenu space
  min-height: 0; // Allow flex item to shrink

  // Custom scrollbar for navigation
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(25, 118, 210, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(25, 118, 210, 0.5);
    }
  }

  // Collapsed state constraints
  .sidebar-nav.collapsed & {
    padding: 8px 0 100px 0 !important;
    overflow-x: hidden !important;
  }
}

.mat-list-item.active {
  background: #e3eaf2;
  color: #1976d2;
  font-weight: 600;
}

.mat-list-item {
  border-radius: 6px;
  margin-bottom: 4px;
  transition: background 0.2s;
}

.mat-list-item:hover {
  background: #f0f4f8;
}

.mat-icon {
  margin-right: 16px;
}

.sidebar-nav {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  overflow: hidden; // Prevent sidebar from scrolling
  padding: 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(180deg, #1976d2 0%, #42a5f5 100%);
    z-index: 1;
  }

  // Collapsed state - CRITICAL: Override width with highest specificity
  &.collapsed {
    width: 64px !important;
    max-width: 64px !important;
    min-width: 64px !important;
    overflow-x: hidden !important;
    overflow-y: hidden !important;
    box-sizing: border-box !important;
    flex-basis: 64px !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
  }
}

// Business Selector Section
.sidebar-business-section {
  flex-shrink: 0; // Prevent compression
  padding: 16px 8px;
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);

  // Collapsed state
  .sidebar-nav.collapsed & {
    padding: 8px 4px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    width: 100% !important;
    max-width: 64px !important;
    overflow: hidden !important;
  }
}

.sidebar-divider {
  margin: 8px 0;

  // Hide dividers in collapsed state
  .sidebar-nav.collapsed & {
    display: none;
  }
}

.sidebar-section-label {
  font-size: 1rem;
  font-weight: 700;
  color: #1976d2;
  letter-spacing: 0.04em;
  margin: 16px 0 6px 24px;
  text-shadow: 0 1px 2px rgba(25, 118, 210, 0.08);
}

.mat-nav-list {
  padding-top: 0;
}

.mat-list-item {
  border-radius: 8px;
  margin: 2px 8px;
  transition: background 0.18s, color 0.18s;
  color: #222;
  font-weight: 500;
  &:hover, &:focus {
    background: #e3eaf2;
    color: var(--mui-palette-primary-main, #1976d2);
  }
  &.active {
    background: #1976d2;
    color: #fff;
    font-weight: 700;
    .mat-icon {
      color: #fff;
    }
  }
}

.mat-icon {
  margin-right: 18px;
  color: var(--mui-palette-primary-main, #1976d2);
  font-variation-settings: 'wght' 400;
  font-size: 1.6rem;
  transition: color 0.18s;
}

.sidebar-business-switcher {
  padding: 0 16px 8px 16px;
  .business-switcher-field {
    width: 100%;
    margin: 0;
    ::ng-deep .mat-form-field-outline {
      border-radius: 8px;
    }
    ::ng-deep .mat-select-value {
      font-weight: 600;
      color: #1976d2;
    }
  }
}

// Collapsed state styles - CRITICAL: Perfect 64px width with centered icons
.sidebar-nav.collapsed {
  // Hide text elements and labels
  .sidebar-section-label,
  .sidebar-expand-icon,
  .sidebar-submenu {
    display: none !important;
  }

  // Hide all text spans but keep icons
  span:not(.mat-icon):not([class*="mat-icon"]) {
    display: none !important;
  }

  // Hide text content in mat-list-item
  .mat-list-item-content span:not(.mat-icon) {
    display: none !important;
  }

  // Center icons and adjust spacing - CRITICAL: Perfect centering
  .mat-list-item {
    justify-content: center !important;
    padding: 8px !important;
    margin: 2px 4px !important;
    min-height: 48px !important;
    width: calc(100% - 8px) !important;
    max-width: 56px !important;
    overflow: hidden !important;
    display: flex !important;
    align-items: center !important;

    .mat-list-item-content {
      justify-content: center !important;
      padding: 0 !important;
      overflow: hidden !important;
      width: 100% !important;
      max-width: 48px !important;
      display: flex !important;
      align-items: center !important;
      flex-direction: row !important;
    }

    .mat-icon {
      margin-right: 0 !important;
      margin-left: 0 !important;
      font-size: 24px !important;
      width: 24px !important;
      height: 24px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }
  }

  // Hide badges in collapsed state
  .mat-badge,
  [matBadge] {
    .mat-badge-content {
      display: none !important;
    }
  }

  // Ensure navigation list is properly constrained
  .mat-nav-list {
    width: 100% !important;
    max-width: 64px !important;
    overflow: hidden !important;
    padding: 8px 0 100px 0 !important;
  }

  // Constrain all child elements
  * {
    max-width: 64px !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
  }

  // Ensure no element can expand beyond 64px
  > * {
    max-width: 64px !important;
    box-sizing: border-box !important;
  }

  // Specifically constrain UserMenu in collapsed state
  app-user-menu {
    max-width: 64px !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
  }

  // Constrain business selector in collapsed state
  app-business-selector {
    max-width: 64px !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
  }
}

.sidebar-toggle-btn {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 8px 8px 0 0;
}

.sidebar-collapsible {
  .sidebar-expand-icon {
    margin-left: auto;
    transition: transform 0.18s;
  }
  .sidebar-submenu {
    padding-left: 32px;
    .sidebar-subitem {
      font-size: 0.97rem;
      .mat-icon {
        font-size: 1.2rem;
        margin-right: 12px;
      }
    }
  }
}

// Tooltip styling for better positioning
::ng-deep .sidebar-tooltip {
  .mat-mdc-tooltip {
    margin-left: 8px !important;
    font-size: 0.875rem !important;
    background: rgba(0, 0, 0, 0.9) !important;
    color: white !important;
    border-radius: 6px !important;
    padding: 8px 12px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }
}

// Responsive design - Mobile layout handled by parent
@media (max-width: 900px) {
  .sidebar-nav {
    // Mobile layout: always show full content
    .mobile-layout & {
      .sidebar-section-label,
      .sidebar-business-switcher,
      .sidebar-divider,
      span:not(.mat-icon),
      .sidebar-expand-icon {
        display: block !important;
      }
    }
  }
}