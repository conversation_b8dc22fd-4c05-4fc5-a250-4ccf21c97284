import { Component, Input, Output, EventEmitter } from '@angular/core';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatDividerModule } from '@angular/material/divider';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatBadgeModule } from '@angular/material/badge';
import { MatTooltipModule } from '@angular/material/tooltip';
import { BusinessSelectorComponent, Business } from './business-selector/business-selector.component';
import { UserMenuComponent } from './user-menu/user-menu.component';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule, MatListModule, MatIconModule, MatButtonModule, MatDividerModule, MatSelectModule, MatOptionModule, MatBadgeModule, MatTooltipModule, BusinessSelectorComponent, UserMenuComponent],
  host: {
    '[class.collapsed]': 'collapsed'
  },
  template: `
    <nav class="sidebar-nav" [class.collapsed]="collapsed" aria-label="Main navigation">
      <!-- Hamburger Toggle Button -->
      <div class="sidebar-toggle-container">
        <button mat-icon-button
                class="hamburger-toggle"
                [class.collapsed]="collapsed"
                (click)="onToggleSidebar()"
                [attr.aria-label]="collapsed ? 'Expand sidebar' : 'Collapse sidebar'"
                [matTooltip]="collapsed ? 'Expand sidebar' : 'Collapse sidebar'"
                matTooltipPosition="right"
                matTooltipClass="sidebar-tooltip">
          <div class="hamburger-icon" [class.collapsed]="collapsed">
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
          </div>
        </button>
      </div>

      <!-- Business Selector at Top -->
      <div class="sidebar-business-section">
        <app-business-selector
          [businesses]="businesses"
          [selectedBusinessIds]="selectedBusinessIds"
          [collapsed]="collapsed"
          (selectionChange)="onBusinessSelectionChange($event)"
        ></app-business-selector>
      </div>

      <mat-divider class="sidebar-divider"></mat-divider>
      <mat-nav-list>
        <div class="sidebar-section-label" *ngIf="!collapsed">Main</div>
        <a mat-list-item routerLink="/dashboard" routerLinkActive="active"
           [matTooltip]="collapsed ? 'Dashboard' : ''"
           matTooltipPosition="right"
           matTooltipClass="sidebar-tooltip"
           aria-label="Dashboard">
          <mat-icon>dashboard</mat-icon>
          <span *ngIf="!collapsed">Dashboard</span>
        </a>
        <a mat-list-item routerLink="/calendar" routerLinkActive="active"
           [matTooltip]="collapsed ? 'Calendar' : ''"
           matTooltipPosition="right"
           matTooltipClass="sidebar-tooltip"
           aria-label="Calendar">
          <mat-icon>calendar_month</mat-icon>
          <span *ngIf="!collapsed">Calendar</span>
        </a>
        <div class="sidebar-collapsible">
          <a mat-list-item (click)="toggleStaffMenu()" [class.active]="staffMenuOpen"
             [matTooltip]="collapsed ? 'Staff' : ''"
             matTooltipPosition="right"
             matTooltipClass="sidebar-tooltip"
             aria-label="Staff">
            <mat-icon>people</mat-icon>
            <span *ngIf="!collapsed">Staff</span>
            <mat-icon class="sidebar-expand-icon" *ngIf="!collapsed">{{ staffMenuOpen ? 'expand_less' : 'expand_more' }}</mat-icon>
          </a>
          <div class="sidebar-submenu" *ngIf="staffMenuOpen && !collapsed">
            <a mat-list-item routerLink="/staff" routerLinkActive="active" class="sidebar-subitem" aria-label="Staff Directory">
              <mat-icon>list</mat-icon>
              <span>Directory</span>
            </a>
            <a mat-list-item routerLink="/staff/new" routerLinkActive="active" class="sidebar-subitem" aria-label="Add Staff">
              <mat-icon>person_add</mat-icon>
              <span>Add Staff</span>
            </a>
          </div>
        </div>
        <a mat-list-item routerLink="/tasks" routerLinkActive="active"
           [matTooltip]="collapsed ? 'Tasks (' + taskBadgeCount + ')' : ''"
           matTooltipPosition="right"
           matTooltipClass="sidebar-tooltip"
           [attr.aria-label]="taskBadgeCount + ' pending tasks'">
          <mat-icon [matBadge]="!collapsed ? taskBadgeCount : null"
            matBadgeColor="warn"
            [matBadgeHidden]="collapsed"
            aria-hidden="false"
          >assignment_turned_in</mat-icon>
          <span *ngIf="!collapsed">Tasks</span>
        </a>
        <a mat-list-item routerLink="/settings" routerLinkActive="active"
           [matTooltip]="collapsed ? 'Settings' : ''"
           matTooltipPosition="right"
           matTooltipClass="sidebar-tooltip"
           aria-label="Settings">
          <mat-icon>settings</mat-icon>
          <span *ngIf="!collapsed">Settings</span>
        </a>
      </mat-nav-list>
      <mat-divider class="sidebar-divider"></mat-divider>
      <div class="sidebar-section-label" *ngIf="!collapsed">Other</div>
      <mat-nav-list>
        <a mat-list-item routerLink="/reports" routerLinkActive="active"
           [matTooltip]="collapsed ? 'Reports' : ''"
           matTooltipPosition="right"
           matTooltipClass="sidebar-tooltip"
           aria-label="Reports">
          <mat-icon>bar_chart</mat-icon>
          <span *ngIf="!collapsed">Reports</span>
        </a>
        <a mat-list-item routerLink="/notifications" routerLinkActive="active"
           [matTooltip]="collapsed ? 'Notifications (' + notificationBadgeCount + ')' : ''"
           matTooltipPosition="right"
           matTooltipClass="sidebar-tooltip"
           [attr.aria-label]="notificationBadgeCount + ' unread notifications'">
          <mat-icon [matBadge]="!collapsed ? notificationBadgeCount : null"
            matBadgeColor="warn"
            [matBadgeHidden]="collapsed"
            aria-hidden="false"
          >notifications</mat-icon>
          <span *ngIf="!collapsed">Notifications</span>
        </a>
      </mat-nav-list>

      <!-- User Menu at Bottom -->
      <app-user-menu [collapsed]="collapsed"></app-user-menu>
    </nav>
  `,
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent {
  @Input() collapsed = false;

  @Output() businessSelectionChange = new EventEmitter<string[]>();
  @Output() toggleSidebar = new EventEmitter<void>();

  staffMenuOpen = false;
  selectedBusinessIds: string[] = ['1'];
  businesses: Business[] = [
    { id: '1', name: 'S&E Jewelers', logoUrl: '' },
    { id: '2', name: 'Diamond District', logoUrl: '' },
    { id: '3', name: 'Gold Rush', logoUrl: '' },
    { id: '4', name: 'Precious Gems Co.', logoUrl: '' }
  ];
  taskBadgeCount = 5;
  notificationBadgeCount = 3;

  toggleStaffMenu() {
    this.staffMenuOpen = !this.staffMenuOpen;
  }

  onToggleSidebar() {
    this.toggleSidebar.emit();
  }

  onBusinessSelectionChange(ids: string[]) {
    this.selectedBusinessIds = ids;
    this.businessSelectionChange.emit(ids);
  }
}